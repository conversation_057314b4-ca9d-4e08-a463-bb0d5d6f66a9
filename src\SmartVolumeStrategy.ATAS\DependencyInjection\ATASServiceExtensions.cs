using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.ObjectPool;
using Serilog;
using SmartVolumeStrategy.ATAS.Performance;
using SmartVolumeStrategy.ATAS.Platform;
using SmartVolumeStrategy.ATAS.PressureDetection;
using SmartVolumeStrategy.ATAS.SignalCoordination;
using SmartVolumeStrategy.ATAS.SignalCoordination.BiasCorrection;
using SmartVolumeStrategy.Core.Infrastructure.DependencyInjection;
using SmartVolumeStrategy.Core.Indicators.Enhanced;
using SmartVolumeStrategy.Core.Regime;
using SmartVolumeStrategy.Core.SignalGeneration;
using ATAS.Strategies.Chart;
using System;

namespace SmartVolumeStrategy.ATAS.DependencyInjection;

/// <summary>
/// Extension methods for configuring ATAS-specific services
/// </summary>
public static class ATASServiceExtensions
{
    /// <summary>
    /// Adds ATAS platform services to the service collection
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddATASServices(this IServiceCollection services)
    {
        // Add logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

        // Configure Serilog
        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Information()
            .WriteTo.Console()
            .WriteTo.File("logs/atas-strategy-.txt", rollingInterval: RollingInterval.Day)
            .CreateLogger();

        // Add Core services (indicators, adaptive system, etc.)
        services.AddIndicatorServices();
        services.AddAdaptiveSystem();
        services.AddSmartVolumeStrategy(Path.GetTempPath());

        // Add ATAS-specific services
        services.AddATASPlatformServices();
        services.AddPerformanceOptimization();
        services.AddPhase2PressureDetection();
        services.AddSignalCoordination();
        services.AddPhase1EnhancedIndicators();
        services.AddPhase2BiasCorrection();
        services.AddNewMarketAdaptiveArchitecture();
        services.AddStrategyServices();

        return services;
    }

    /// <summary>
    /// Adds ATAS platform abstraction services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddATASPlatformServices(this IServiceCollection services)
    {
        // Register platform interfaces
        // Note: In real ATAS integration, these would be implemented by ATAS-provided classes
        services.AddSingleton<ITradingManager, MockTradingManager>();
        services.AddSingleton<IInstrumentInfo, MockInstrumentInfo>();

        return services;
    }

    /// <summary>
    /// Adds performance optimization services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPerformanceOptimization(this IServiceCollection services)
    {
        // Add object pooling for calculations
        services.AddSingleton<ObjectPool<IndicatorCalculation>>(provider =>
        {
            var poolProvider = new DefaultObjectPoolProvider();
            var policy = new IndicatorCalculationPoolPolicy();
            return poolProvider.Create(policy);
        });

        // Add optimized indicator processor
        services.AddSingleton<OptimizedIndicatorProcessor>();

        return services;
    }

    /// <summary>
    /// Adds strategy-specific services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddStrategyServices(this IServiceCollection services)
    {
        // Register the main strategy
        services.AddTransient<HighProbabilityScalpingStrategy>();

        // Add strategy factory if needed
        services.AddSingleton<IStrategyFactory, StrategyFactory>();

        return services;
    }

    /// <summary>
    /// Adds Phase 2 Bias Correction services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPhase2BiasCorrection(this IServiceCollection services)
    {
        // Register Systematic Bias Corrector
        services.AddSingleton<SystematicBiasCorrector>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<SystematicBiasCorrector>>();
            return new SystematicBiasCorrector(logger);
        });

        return services;
    }

    /// <summary>
    /// Validates that all required services are properly registered
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection ValidateATASServices(this IServiceCollection services)
    {
        var serviceProvider = services.BuildServiceProvider();

        try
        {
            // Validate core services
            services.ValidateIndicatorServices();

            // Validate ATAS-specific services
            serviceProvider.GetRequiredService<ITradingManager>();
            serviceProvider.GetRequiredService<IInstrumentInfo>();
            serviceProvider.GetRequiredService<OptimizedIndicatorProcessor>();
            serviceProvider.GetRequiredService<ObjectPool<IndicatorCalculation>>();

            // Validate Phase 2 detectors
            serviceProvider.GetRequiredService<IcebergOrderDetector>();
            serviceProvider.GetRequiredService<SpoofingDetector>();
            serviceProvider.GetRequiredService<BlockTradeIdentifier>();
            serviceProvider.GetRequiredService<PressureZScoreAnalyzer>();
            serviceProvider.GetRequiredService<VolumeClusteringAnalyzer>();
            serviceProvider.GetRequiredService<LiquidityFragmentationAnalyzer>();

            // Validate Signal Coordination System
            serviceProvider.GetRequiredService<SignalCoordinationSystem>();
            serviceProvider.GetRequiredService<SignalCoordinationConfig>();

            // Validate Phase 1 Enhanced Indicators
            serviceProvider.GetRequiredService<MultiTimeframeIndicatorEngine>();
            serviceProvider.GetRequiredService<AdaptiveParameterManager>();
            serviceProvider.GetRequiredService<VolumeWeightedRSI>();
            serviceProvider.GetRequiredService<VolumeWeightedBollingerBands>();

            // Validate Phase 2 Bias Correction
            serviceProvider.GetRequiredService<SystematicBiasCorrector>();

            // Validate New Market-Adaptive Architecture
            serviceProvider.GetRequiredService<MarketRegimeDetector>();
            serviceProvider.GetRequiredService<AdaptiveThresholdManager>();
            serviceProvider.GetRequiredService<ProgressiveConfidenceCalculator>();
            serviceProvider.GetRequiredService<EmergencySignalDetector>();
            serviceProvider.GetRequiredService<SimplifiedSignalCoordinator>();

            serviceProvider.GetRequiredService<HighProbabilityScalpingStrategy>();

            var logger = serviceProvider.GetRequiredService<ILogger<StrategyFactory>>();
            logger.LogInformation("All ATAS services validated successfully");
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException("Failed to validate ATAS services registration", ex);
        }
        finally
        {
            serviceProvider.Dispose();
        }

        return services;
    }

    /// <summary>
    /// Adds Phase 2 Enhanced Market Pressure Detection services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPhase2PressureDetection(this IServiceCollection services)
    {
        // Register Phase 2 detectors as singletons for performance
        services.AddSingleton<IcebergOrderDetector>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<IcebergOrderDetector>>();
            return new IcebergOrderDetector(logger);
        });

        services.AddSingleton<SpoofingDetector>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<SpoofingDetector>>();
            return new SpoofingDetector(logger);
        });

        services.AddSingleton<BlockTradeIdentifier>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<BlockTradeIdentifier>>();
            return new BlockTradeIdentifier(logger);
        });

        services.AddSingleton<PressureZScoreAnalyzer>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<PressureZScoreAnalyzer>>();
            return new PressureZScoreAnalyzer(logger);
        });

        services.AddSingleton<VolumeClusteringAnalyzer>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VolumeClusteringAnalyzer>>();
            return new VolumeClusteringAnalyzer(logger);
        });

        services.AddSingleton<LiquidityFragmentationAnalyzer>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<LiquidityFragmentationAnalyzer>>();
            return new LiquidityFragmentationAnalyzer(logger);
        });

        return services;
    }

    /// <summary>
    /// Adds Signal Coordination System services
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddSignalCoordination(this IServiceCollection services)
    {
        // Register Signal Coordination System
        services.AddSingleton<SignalCoordinationSystem>();

        // Register configuration
        services.AddSingleton<SignalCoordinationConfig>();

        return services;
    }

    /// <summary>
    /// Adds Phase 1 Enhanced Indicator services for multi-timeframe and volume-weighted analysis
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddPhase1EnhancedIndicators(this IServiceCollection services)
    {
        // Register Multi-Timeframe Analysis Engine with fallback logging
        services.AddSingleton<MultiTimeframeIndicatorEngine>(provider =>
        {
            try
            {
                var logger = provider.GetRequiredService<ILogger<MultiTimeframeIndicatorEngine>>();
                return new MultiTimeframeIndicatorEngine(logger);
            }
            catch (Exception ex)
            {
                // Fallback: Create with null logger if DI fails
                Console.WriteLine($"Warning: Failed to resolve ILogger<MultiTimeframeIndicatorEngine>: {ex.Message}");
                Console.WriteLine("Creating MultiTimeframeIndicatorEngine with fallback logging");

                // Create a simple console logger as fallback
                var loggerFactory = LoggerFactory.Create(builder => builder.AddConsole());
                var fallbackLogger = loggerFactory.CreateLogger<MultiTimeframeIndicatorEngine>();
                return new MultiTimeframeIndicatorEngine(fallbackLogger);
            }
        });

        // Register Adaptive Parameter Manager
        services.AddSingleton<AdaptiveParameterManager>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<AdaptiveParameterManager>>();
            return new AdaptiveParameterManager(logger);
        });

        // Register Volume-Weighted RSI
        services.AddTransient<VolumeWeightedRSI>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VolumeWeightedRSI>>();
            return new VolumeWeightedRSI(logger);
        });

        // Register Volume-Weighted Bollinger Bands
        services.AddTransient<VolumeWeightedBollingerBands>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<VolumeWeightedBollingerBands>>();
            return new VolumeWeightedBollingerBands(logger);
        });

        return services;
    }

    /// <summary>
    /// PERMANENT ARCHITECTURAL OVERHAUL: Adds new market-adaptive signal generation services
    /// This replaces the over-engineered complex system with simplified, market-adaptive approach
    /// </summary>
    /// <param name="services">Service collection</param>
    /// <returns>Service collection for chaining</returns>
    public static IServiceCollection AddNewMarketAdaptiveArchitecture(this IServiceCollection services)
    {
        // Register Market Regime Detection System
        services.AddSingleton<SmartVolumeStrategy.Core.Regime.MarketRegimeDetector>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<SmartVolumeStrategy.Core.Regime.MarketRegimeDetector>>();
            return new SmartVolumeStrategy.Core.Regime.MarketRegimeDetector(logger);
        });

        // Register Adaptive Threshold Manager
        services.AddSingleton<AdaptiveThresholdManager>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<AdaptiveThresholdManager>>();
            return new AdaptiveThresholdManager(logger);
        });

        // Register Progressive Confidence Calculator
        services.AddSingleton<ProgressiveConfidenceCalculator>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<ProgressiveConfidenceCalculator>>();
            return new ProgressiveConfidenceCalculator(logger);
        });

        // Register Emergency Signal Detector
        services.AddSingleton<EmergencySignalDetector>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<EmergencySignalDetector>>();
            return new EmergencySignalDetector(logger);
        });

        // Register Simplified Signal Coordinator
        services.AddSingleton<SimplifiedSignalCoordinator>(provider =>
        {
            var logger = provider.GetRequiredService<ILogger<SimplifiedSignalCoordinator>>();
            var regimeDetector = provider.GetRequiredService<SmartVolumeStrategy.Core.Regime.MarketRegimeDetector>();
            var thresholdManager = provider.GetRequiredService<AdaptiveThresholdManager>();
            var confidenceCalculator = provider.GetRequiredService<ProgressiveConfidenceCalculator>();
            var emergencyDetector = provider.GetRequiredService<EmergencySignalDetector>();

            return new SimplifiedSignalCoordinator(
                logger,
                regimeDetector,
                thresholdManager,
                confidenceCalculator,
                emergencyDetector);
        });

        return services;
    }
}

/// <summary>
/// Object pool policy for IndicatorCalculation objects
/// </summary>
public class IndicatorCalculationPoolPolicy : IPooledObjectPolicy<IndicatorCalculation>
{
    public IndicatorCalculation Create()
    {
        return new IndicatorCalculation();
    }

    public bool Return(IndicatorCalculation obj)
    {
        // Reset the object for reuse
        obj.Reset();
        return true;
    }
}

/// <summary>
/// Strategy factory interface
/// </summary>
public interface IStrategyFactory
{
    /// <summary>
    /// Creates a new strategy instance
    /// </summary>
    /// <returns>Strategy instance</returns>
    HighProbabilityScalpingStrategy CreateStrategy();
}

/// <summary>
/// Strategy factory implementation
/// </summary>
public class StrategyFactory : IStrategyFactory
{
    private readonly IServiceProvider _serviceProvider;

    public StrategyFactory(IServiceProvider serviceProvider)
    {
        _serviceProvider = serviceProvider;
    }

    public HighProbabilityScalpingStrategy CreateStrategy()
    {
        return _serviceProvider.GetRequiredService<HighProbabilityScalpingStrategy>();
    }
}

/// <summary>
/// Mock trading manager for development and testing
/// </summary>
public class MockTradingManager : ITradingManager
{
    private readonly ILogger<MockTradingManager> _logger;
    private readonly List<IOrder> _activeOrders = new();
    private int _orderIdCounter = 1;

    public MockTradingManager(ILogger<MockTradingManager> logger)
    {
        _logger = logger;
    }

    public async Task<IOrder?> PlaceOrderAsync(OrderRequest orderRequest)
    {
        await Task.Delay(10); // Simulate network latency

        var order = new MockOrder
        {
            Id = $"ORDER_{_orderIdCounter++}",
            Side = orderRequest.Side,
            Type = orderRequest.Type,
            Quantity = orderRequest.Quantity,
            Price = orderRequest.Price ?? 100.0m,
            State = OrderState.Active,
            CreatedTime = DateTime.UtcNow,
            LastUpdateTime = DateTime.UtcNow,
            StopLoss = orderRequest.StopLoss,
            TakeProfit = orderRequest.TakeProfit,
            Comment = orderRequest.Comment
        };

        _activeOrders.Add(order);
        _logger.LogInformation("Mock order placed: {OrderId}", order.Id);

        return order;
    }

    public async Task<bool> ModifyOrderAsync(string orderId, decimal? newPrice = null, decimal? newQuantity = null)
    {
        await Task.Delay(5);
        _logger.LogInformation("Mock order modified: {OrderId}", orderId);
        return true;
    }

    public async Task<bool> CancelOrderAsync(string orderId)
    {
        await Task.Delay(5);
        var order = _activeOrders.FirstOrDefault(o => o.Id == orderId);
        if (order != null)
        {
            _activeOrders.Remove(order);
            _logger.LogInformation("Mock order cancelled: {OrderId}", orderId);
        }
        return true;
    }

    public IPosition GetCurrentPosition()
    {
        return new MockPosition();
    }

    public IList<IOrder> GetActiveOrders()
    {
        return _activeOrders.ToList();
    }
}

/// <summary>
/// Mock instrument info for development and testing
/// </summary>
public class MockInstrumentInfo : IInstrumentInfo
{
    public string Symbol => "ES";
    public string Description => "E-mini S&P 500";
    public decimal TickSize => 0.25m;
    public decimal PointValue => 12.50m;
    public decimal MinQuantity => 1m;
    public decimal MaxQuantity => 1000m;
    public decimal QuantityStep => 1m;
    public string Currency => "USD";
    public string Exchange => "CME";
}
