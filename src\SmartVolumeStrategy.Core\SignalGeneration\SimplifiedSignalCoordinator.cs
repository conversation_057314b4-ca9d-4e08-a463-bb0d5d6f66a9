using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.MarketRegime;
using SmartVolumeStrategy.Core.Models;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace SmartVolumeStrategy.Core.SignalGeneration
{
    /// <summary>
    /// Simplified signal coordination system
    /// Removes complex bottlenecks and focuses on market-adaptive signal generation
    /// </summary>
    public class SimplifiedSignalCoordinator : IDisposable
    {
        private readonly ILogger<SimplifiedSignalCoordinator> _logger;
        private readonly MarketRegimeDetector _regimeDetector;
        private readonly AdaptiveThresholdManager _thresholdManager;
        private readonly ProgressiveConfidenceCalculator _confidenceCalculator;
        private readonly EmergencySignalDetector _emergencyDetector;

        // Performance tracking
        private long _totalSignals;
        private long _emergencySignals;
        private long _regimeAdaptations;
        private readonly object _statsLock = new();

        // Configuration
        private const double MaxProcessingTimeMs = 20.0;
        private const double EmergencyProcessingTimeMs = 5.0;

        public SimplifiedSignalCoordinator(
            ILogger<SimplifiedSignalCoordinator> logger,
            MarketRegimeDetector regimeDetector,
            AdaptiveThresholdManager thresholdManager,
            ProgressiveConfidenceCalculator confidenceCalculator,
            EmergencySignalDetector emergencyDetector)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _regimeDetector = regimeDetector ?? throw new ArgumentNullException(nameof(regimeDetector));
            _thresholdManager = thresholdManager ?? throw new ArgumentNullException(nameof(thresholdManager));
            _confidenceCalculator = confidenceCalculator ?? throw new ArgumentNullException(nameof(confidenceCalculator));
            _emergencyDetector = emergencyDetector ?? throw new ArgumentNullException(nameof(emergencyDetector));

            _logger.LogInformation("🎯 Simplified Signal Coordinator initialized");
        }

        /// <summary>
        /// Generates trading signal using simplified, market-adaptive approach
        /// </summary>
        public async Task<SimplifiedSignalResult> GenerateSignalAsync(
            MarketData marketData,
            TradingSignal? baseSignal = null,
            CancellationToken cancellationToken = default)
        {
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // STEP 1: Emergency Detection (Highest Priority)
                var emergencyResult = _emergencyDetector.DetectEmergencyConditions(marketData);
                if (emergencyResult.HasEmergency && emergencyResult.EmergencySignal != null)
                {
                    stopwatch.Stop();
                    return CreateEmergencySignalResult(emergencyResult, stopwatch.Elapsed.TotalMilliseconds);
                }

                // STEP 2: Market Regime Analysis
                var regimeAnalysis = _regimeDetector.AnalyzeMarketRegime(marketData);

                // STEP 3: Adaptive Threshold Calculation
                var thresholdResult = _thresholdManager.GetAdaptiveThresholds(regimeAnalysis);

                // STEP 4: Generate Base Signal (if not provided)
                if (baseSignal == null)
                {
                    baseSignal = await GenerateBaseSignal(marketData, regimeAnalysis, cancellationToken);
                }

                // STEP 5: Progressive Confidence Calculation
                var confidenceResult = _confidenceCalculator.CalculateConfidence(
                    baseSignal, regimeAnalysis, marketData);

                // STEP 6: Apply Adaptive Thresholds
                var finalSignal = ApplyAdaptiveThresholds(confidenceResult, thresholdResult);

                stopwatch.Stop();

                // STEP 7: Create Result
                var result = CreateSignalResult(finalSignal, regimeAnalysis, thresholdResult, 
                    confidenceResult, stopwatch.Elapsed.TotalMilliseconds);

                // Update statistics
                UpdateStatistics(result);

                // Log result
                LogSignalResult(result);

                return result;
            }
            catch (Exception ex)
            {
                stopwatch.Stop();
                _logger.LogError(ex, "❌ Error generating simplified signal");
                return CreateErrorResult(ex, stopwatch.Elapsed.TotalMilliseconds);
            }
        }

        /// <summary>
        /// Gets performance statistics
        /// </summary>
        public SimplifiedCoordinatorStatistics GetStatistics()
        {
            lock (_statsLock)
            {
                return new SimplifiedCoordinatorStatistics
                {
                    TotalSignals = _totalSignals,
                    EmergencySignals = _emergencySignals,
                    RegimeAdaptations = _regimeAdaptations,
                    EmergencySignalRate = _totalSignals > 0 ? (double)_emergencySignals / _totalSignals : 0.0,
                    RegimeDetectorStats = _regimeDetector.GetRegimeStabilityTrend(),
                    ThresholdManagerStats = _thresholdManager.GetStatistics(),
                    EmergencyDetectorStats = _emergencyDetector.GetStatistics()
                };
            }
        }

        private async Task<TradingSignal> GenerateBaseSignal(
            MarketData marketData, 
            MarketRegimeAnalysis regimeAnalysis,
            CancellationToken cancellationToken)
        {
            // Simplified base signal generation based on regime
            var direction = DetermineSignalDirection(marketData, regimeAnalysis);
            var baseConfidence = CalculateBaseConfidence(marketData, regimeAnalysis);

            return new TradingSignal
            {
                Direction = direction,
                Confidence = baseConfidence,
                Reason = $"Base signal for {regimeAnalysis.PrimaryRegime} regime",
                Timestamp = marketData.Timestamp,
                Source = "SimplifiedCoordinator"
            };
        }

        private TradeDirection DetermineSignalDirection(MarketData marketData, MarketRegimeAnalysis regimeAnalysis)
        {
            // Simple direction determination based on price action and trend
            var priceChange = marketData.Close - marketData.Open;
            var trendDirection = regimeAnalysis.TrendProfile.TrendDirection;

            // Combine price action with trend analysis
            if (priceChange > 0 && trendDirection > 0.2)
                return TradeDirection.Buy;
            else if (priceChange < 0 && trendDirection < -0.2)
                return TradeDirection.Sell;
            else
                return TradeDirection.Unknown;
        }

        private double CalculateBaseConfidence(MarketData marketData, MarketRegimeAnalysis regimeAnalysis)
        {
            // Base confidence calculation
            var regimeStrength = regimeAnalysis.RegimeStrength;
            var trendStrength = regimeAnalysis.TrendProfile.TrendStrength;
            var volumeConfidence = Math.Min(regimeAnalysis.VolumeProfile.RelativeVolume / 2.0, 1.0);

            // Weighted combination
            var baseConfidence = (regimeStrength * 0.4) + (trendStrength * 0.4) + (volumeConfidence * 0.2);

            return Math.Max(0.1, Math.Min(0.8, baseConfidence)); // Clamp between 10% and 80%
        }

        private TradingSignal ApplyAdaptiveThresholds(
            ProgressiveConfidenceResult confidenceResult,
            AdaptiveThresholdResult thresholdResult)
        {
            var config = thresholdResult.Configuration;
            var meetsThreshold = confidenceResult.FinalConfidence >= config.ConfidenceThreshold;

            if (!meetsThreshold)
            {
                return TradingSignal.CreateNoSignal(
                    $"Confidence {confidenceResult.FinalConfidence:P1} below {config.Regime} threshold {config.ConfidenceThreshold:P1}");
            }

            // Create final signal
            return new TradingSignal
            {
                Direction = confidenceResult.BaseSignal.Direction,
                Confidence = confidenceResult.FinalConfidence,
                Reason = $"{config.Regime} regime signal - {confidenceResult.CalculationReason}",
                Timestamp = confidenceResult.Timestamp,
                Source = "SimplifiedCoordinator",
                Metadata = new Dictionary<string, object>
                {
                    ["Regime"] = config.Regime.ToString(),
                    ["RegimeStrength"] = thresholdResult.RegimeAnalysis.RegimeStrength,
                    ["ThresholdUsed"] = config.ConfidenceThreshold,
                    ["IsEmergency"] = confidenceResult.IsEmergencySignal
                }
            };
        }

        private SimplifiedSignalResult CreateEmergencySignalResult(
            EmergencyDetectionResult emergencyResult, 
            double processingTimeMs)
        {
            var emergencySignal = emergencyResult.EmergencySignal!;

            var tradingSignal = new TradingSignal
            {
                Direction = emergencySignal.Direction,
                Confidence = emergencySignal.Confidence,
                Reason = $"EMERGENCY: {emergencySignal.Reason}",
                Timestamp = emergencySignal.Timestamp,
                Source = "EmergencyDetector",
                Metadata = new Dictionary<string, object>
                {
                    ["IsEmergency"] = true,
                    ["EmergencySeverity"] = emergencySignal.Severity,
                    ["EmergencyConditions"] = emergencySignal.Conditions.Count
                }
            };

            return new SimplifiedSignalResult
            {
                Signal = tradingSignal,
                IsEmergencySignal = true,
                RegimeAnalysis = null, // Emergency bypasses regime analysis
                ThresholdResult = null,
                ConfidenceResult = null,
                EmergencyResult = emergencyResult,
                ProcessingTimeMs = processingTimeMs,
                Timestamp = DateTime.UtcNow
            };
        }

        private SimplifiedSignalResult CreateSignalResult(
            TradingSignal signal,
            MarketRegimeAnalysis regimeAnalysis,
            AdaptiveThresholdResult thresholdResult,
            ProgressiveConfidenceResult confidenceResult,
            double processingTimeMs)
        {
            return new SimplifiedSignalResult
            {
                Signal = signal,
                IsEmergencySignal = false,
                RegimeAnalysis = regimeAnalysis,
                ThresholdResult = thresholdResult,
                ConfidenceResult = confidenceResult,
                EmergencyResult = null,
                ProcessingTimeMs = processingTimeMs,
                Timestamp = DateTime.UtcNow
            };
        }

        private SimplifiedSignalResult CreateErrorResult(Exception exception, double processingTimeMs)
        {
            return new SimplifiedSignalResult
            {
                Signal = TradingSignal.CreateNoSignal($"Error: {exception.Message}"),
                IsEmergencySignal = false,
                RegimeAnalysis = null,
                ThresholdResult = null,
                ConfidenceResult = null,
                EmergencyResult = null,
                ProcessingTimeMs = processingTimeMs,
                Timestamp = DateTime.UtcNow,
                Error = exception
            };
        }

        private void UpdateStatistics(SimplifiedSignalResult result)
        {
            lock (_statsLock)
            {
                _totalSignals++;

                if (result.IsEmergencySignal)
                {
                    _emergencySignals++;
                }

                if (result.RegimeAnalysis != null)
                {
                    _regimeAdaptations++;
                }
            }
        }

        private void LogSignalResult(SimplifiedSignalResult result)
        {
            if (result.IsEmergencySignal)
            {
                _logger.LogWarning("🚨 EMERGENCY SIGNAL: {Direction} - Confidence: {Confidence:P1} - Time: {ProcessingTime:F2}ms",
                    result.Signal.Direction, result.Signal.Confidence, result.ProcessingTimeMs);
            }
            else if (result.Signal.Direction != TradeDirection.Unknown)
            {
                var regime = result.RegimeAnalysis?.PrimaryRegime.ToString() ?? "Unknown";
                _logger.LogInformation("📈 SIGNAL: {Direction} - Confidence: {Confidence:P1} - Regime: {Regime} - Time: {ProcessingTime:F2}ms",
                    result.Signal.Direction, result.Signal.Confidence, regime, result.ProcessingTimeMs);
            }
            else
            {
                _logger.LogDebug("⚪ NO SIGNAL: {Reason} - Time: {ProcessingTime:F2}ms",
                    result.Signal.Reason, result.ProcessingTimeMs);
            }

            // Performance warning
            var maxTime = result.IsEmergencySignal ? EmergencyProcessingTimeMs : MaxProcessingTimeMs;
            if (result.ProcessingTimeMs > maxTime)
            {
                _logger.LogWarning("⚠️ Signal processing exceeded {MaxTime}ms: {ActualTime:F2}ms",
                    maxTime, result.ProcessingTimeMs);
            }
        }

        public void Dispose()
        {
            _regimeDetector?.Dispose();
            _emergencyDetector?.Dispose();
        }
    }

    /// <summary>
    /// Result of simplified signal coordination
    /// </summary>
    public class SimplifiedSignalResult
    {
        /// <summary>
        /// Final trading signal generated
        /// </summary>
        public TradingSignal Signal { get; set; } = new();

        /// <summary>
        /// Whether this was an emergency signal
        /// </summary>
        public bool IsEmergencySignal { get; set; }

        /// <summary>
        /// Market regime analysis (null for emergency signals)
        /// </summary>
        public MarketRegimeAnalysis? RegimeAnalysis { get; set; }

        /// <summary>
        /// Adaptive threshold result (null for emergency signals)
        /// </summary>
        public AdaptiveThresholdResult? ThresholdResult { get; set; }

        /// <summary>
        /// Progressive confidence result (null for emergency signals)
        /// </summary>
        public ProgressiveConfidenceResult? ConfidenceResult { get; set; }

        /// <summary>
        /// Emergency detection result (null for normal signals)
        /// </summary>
        public EmergencyDetectionResult? EmergencyResult { get; set; }

        /// <summary>
        /// Total processing time in milliseconds
        /// </summary>
        public double ProcessingTimeMs { get; set; }

        /// <summary>
        /// When this result was generated
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Error information if signal generation failed
        /// </summary>
        public Exception? Error { get; set; }
    }

    /// <summary>
    /// Performance statistics for simplified signal coordinator
    /// </summary>
    public class SimplifiedCoordinatorStatistics
    {
        /// <summary>
        /// Total signals generated
        /// </summary>
        public long TotalSignals { get; set; }

        /// <summary>
        /// Number of emergency signals generated
        /// </summary>
        public long EmergencySignals { get; set; }

        /// <summary>
        /// Number of regime adaptations performed
        /// </summary>
        public long RegimeAdaptations { get; set; }

        /// <summary>
        /// Rate of emergency signals (0.0 to 1.0)
        /// </summary>
        public double EmergencySignalRate { get; set; }

        /// <summary>
        /// Regime detector stability trend
        /// </summary>
        public double RegimeDetectorStats { get; set; }

        /// <summary>
        /// Threshold manager statistics
        /// </summary>
        public AdaptiveThresholdStatistics ThresholdManagerStats { get; set; } = new();

        /// <summary>
        /// Emergency detector statistics
        /// </summary>
        public EmergencyDetectionStatistics EmergencyDetectorStats { get; set; } = new();
    }
}
