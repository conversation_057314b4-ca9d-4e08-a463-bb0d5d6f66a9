using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Core.Regime
{
    /// <summary>
    /// Market regime classification for adaptive strategy behavior
    /// </summary>
    public enum MarketRegime
    {
        /// <summary>
        /// Strong directional moves with high volume and velocity
        /// Characteristics: Trend strength greater than 0.7, Volume greater than 1.5x average, Low noise
        /// Strategy: Aggressive thresholds, fast execution, single indicator sufficient
        /// </summary>
        Momentum,

        /// <summary>
        /// Sideways movement with low trend strength
        /// Characteristics: Trend strength less than 0.3, Normal volume, High noise
        /// Strategy: Conservative thresholds, multiple confirmations required
        /// </summary>
        Ranging,

        /// <summary>
        /// High volatility with mixed signals and rapid changes
        /// Characteristics: High volatility greater than 2x average, Mixed directions
        /// Strategy: Medium thresholds, adaptive to rapid changes
        /// </summary>
        Volatile,

        /// <summary>
        /// Low activity periods with minimal movement
        /// Characteristics: Low volume less than 0.7x average, Low volatility
        /// Strategy: High thresholds, patient approach, quality over quantity
        /// </summary>
        Quiet,

        /// <summary>
        /// Transitioning between regimes - unstable period
        /// Characteristics: Conflicting signals, changing patterns
        /// Strategy: Cautious approach, wait for regime stabilization
        /// </summary>
        Transition
    }

    /// <summary>
    /// Comprehensive market regime analysis result
    /// </summary>
    public class MarketRegimeAnalysis
    {
        /// <summary>
        /// Primary detected market regime
        /// </summary>
        public MarketRegime PrimaryRegime { get; set; }

        /// <summary>
        /// Secondary regime (for mixed conditions)
        /// </summary>
        public MarketRegime? SecondaryRegime { get; set; }

        /// <summary>
        /// Strength of the regime classification (0.0 to 1.0)
        /// Higher values indicate more confident regime detection
        /// </summary>
        public double RegimeStrength { get; set; }

        /// <summary>
        /// Stability of the current regime (0.0 to 1.0)
        /// Higher values indicate the regime is likely to persist
        /// </summary>
        public double RegimeStability { get; set; }

        /// <summary>
        /// Probability of regime transition in next period (0.0 to 1.0)
        /// </summary>
        public double TransitionProbability { get; set; }

        /// <summary>
        /// Detailed volatility profile
        /// </summary>
        public VolatilityProfile VolatilityProfile { get; set; } = new();

        /// <summary>
        /// Detailed trend profile
        /// </summary>
        public TrendProfile TrendProfile { get; set; } = new();

        /// <summary>
        /// Detailed volume profile
        /// </summary>
        public VolumeProfile VolumeProfile { get; set; } = new();

        /// <summary>
        /// When this analysis was performed
        /// </summary>
        public DateTime Timestamp { get; set; }

        /// <summary>
        /// Additional metadata for debugging and analysis
        /// </summary>
        public Dictionary<string, object> Metadata { get; set; } = new();
    }

    /// <summary>
    /// Volatility characteristics of the current market
    /// </summary>
    public class VolatilityProfile
    {
        /// <summary>
        /// Current volatility level relative to historical average
        /// </summary>
        public double RelativeVolatility { get; set; }

        /// <summary>
        /// Volatility trend (increasing, decreasing, stable)
        /// </summary>
        public double VolatilityTrend { get; set; }

        /// <summary>
        /// Volatility clustering factor (tendency for high/low vol to persist)
        /// </summary>
        public double ClusteringFactor { get; set; }

        /// <summary>
        /// Intraday volatility pattern
        /// </summary>
        public double IntradayPattern { get; set; }
    }

    /// <summary>
    /// Trend characteristics of the current market
    /// </summary>
    public class TrendProfile
    {
        /// <summary>
        /// Overall trend strength (0.0 = no trend, 1.0 = very strong trend)
        /// </summary>
        public double TrendStrength { get; set; }

        /// <summary>
        /// Trend direction (-1.0 = strong down, 0.0 = no trend, 1.0 = strong up)
        /// </summary>
        public double TrendDirection { get; set; }

        /// <summary>
        /// Trend consistency (how consistent the trend has been)
        /// </summary>
        public double TrendConsistency { get; set; }

        /// <summary>
        /// Trend acceleration (is the trend accelerating or decelerating)
        /// </summary>
        public double TrendAcceleration { get; set; }

        /// <summary>
        /// Multiple timeframe trend alignment
        /// </summary>
        public double MultiTimeframeAlignment { get; set; }
    }

    /// <summary>
    /// Volume characteristics of the current market
    /// </summary>
    public class VolumeProfile
    {
        /// <summary>
        /// Current volume relative to average
        /// </summary>
        public double RelativeVolume { get; set; }

        /// <summary>
        /// Volume trend (increasing, decreasing, stable)
        /// </summary>
        public double VolumeTrend { get; set; }

        /// <summary>
        /// Volume-price relationship strength
        /// </summary>
        public double VolumePriceCorrelation { get; set; }

        /// <summary>
        /// Volume distribution (concentrated vs distributed)
        /// </summary>
        public double VolumeDistribution { get; set; }

        /// <summary>
        /// Institutional volume indicators
        /// </summary>
        public double InstitutionalVolumeSignal { get; set; }
    }

    /// <summary>
    /// Regime-specific configuration parameters
    /// </summary>
    public class RegimeConfiguration
    {
        /// <summary>
        /// Target market regime
        /// </summary>
        public MarketRegime Regime { get; set; }

        /// <summary>
        /// Confidence threshold for this regime
        /// </summary>
        public double ConfidenceThreshold { get; set; }

        /// <summary>
        /// Required indicator alignment count
        /// </summary>
        public int IndicatorAlignment { get; set; }

        /// <summary>
        /// Minimum volume threshold
        /// </summary>
        public double VolumeThreshold { get; set; }

        /// <summary>
        /// Minimum signal interval in seconds
        /// </summary>
        public int SignalIntervalSeconds { get; set; }

        /// <summary>
        /// Maximum signals per hour for this regime
        /// </summary>
        public int MaxSignalsPerHour { get; set; }

        /// <summary>
        /// Risk multiplier for position sizing
        /// </summary>
        public double RiskMultiplier { get; set; }

        /// <summary>
        /// Signal urgency multiplier
        /// </summary>
        public double UrgencyMultiplier { get; set; }

        /// <summary>
        /// Whether emergency bypasses are allowed in this regime
        /// </summary>
        public bool AllowEmergencyBypass { get; set; }
    }

    /// <summary>
    /// Multi-timeframe regime validation result
    /// </summary>
    public class RegimeValidationResult
    {
        /// <summary>
        /// Consensus regime across timeframes
        /// </summary>
        public MarketRegime ConsensusRegime { get; set; }

        /// <summary>
        /// Strength of consensus (0.0 to 1.0)
        /// </summary>
        public double ConsensusStrength { get; set; }

        /// <summary>
        /// Whether there's strong consensus (greater than or equal to 67% agreement)
        /// </summary>
        public bool IsStrongConsensus { get; set; }

        /// <summary>
        /// Conflicting regimes detected in other timeframes
        /// </summary>
        public List<KeyValuePair<MarketRegime, int>> ConflictingRegimes { get; set; } = new();

        /// <summary>
        /// Timeframe-specific regime breakdown
        /// </summary>
        public Dictionary<TimeSpan, MarketRegime> TimeframeBreakdown { get; set; } = new();
    }
}
