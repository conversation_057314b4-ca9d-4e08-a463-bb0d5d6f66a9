using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Regime;
using SmartVolumeStrategy.Core.Models;
using SmartVolumeStrategy.Core.Utils;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;

namespace SmartVolumeStrategy.Core.SignalGeneration
{
    /// <summary>
    /// Emergency signal detection system
    /// Bypasses normal filtering for extreme market conditions
    /// </summary>
    public class EmergencySignalDetector : IDisposable
    {
        private readonly ILogger<EmergencySignalDetector> _logger;
        private readonly CircularBuffer<MarketData> _marketHistory;
        private readonly CircularBuffer<EmergencySignal> _emergencyHistory;
        private readonly object _detectionLock = new();

        // Emergency detection thresholds
        private const double ExplosiveVelocityThreshold = 0.008;  // 0.8% price velocity
        private const double MassiveVolumeThreshold = 4.0;        // 4x volume spike
        private const double PriceGapThreshold = 0.01;            // 1% price gap
        private const double VelocityAccelerationThreshold = 3.0; // 3x velocity acceleration
        private const double LiquidityVacuumThreshold = 0.015;    // 1.5% rapid movement

        // Rate limiting for emergency signals
        private const int MaxEmergencySignalsPerMinute = 5;
        private const int EmergencySignalCooldownSeconds = 10;

        // History tracking
        private const int MarketHistorySize = 100;
        private const int EmergencyHistorySize = 50;

        // Baseline calculations
        private double _baselineVolume;
        private double _baselineVolatility;
        private double _baselineVelocity;
        private bool _baselineEstablished;
        private DateTime _lastEmergencySignal;

        public EmergencySignalDetector(ILogger<EmergencySignalDetector> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _marketHistory = new CircularBuffer<MarketData>(MarketHistorySize);
            _emergencyHistory = new CircularBuffer<EmergencySignal>(EmergencyHistorySize);
            _lastEmergencySignal = DateTime.MinValue;

            _logger.LogInformation("🚨 Emergency Signal Detector initialized");
        }

        /// <summary>
        /// Detects emergency market conditions that warrant immediate signal generation
        /// </summary>
        public EmergencyDetectionResult DetectEmergencyConditions(MarketData marketData)
        {
            var stopwatch = Stopwatch.StartNew();

            lock (_detectionLock)
            {
                // Add to history
                _marketHistory.Add(marketData);

                // Update baselines
                UpdateBaselines();

                // Check rate limiting
                if (!CanGenerateEmergencySignal())
                {
                    return CreateNoEmergencyResult("Rate limited");
                }

                // Detect emergency conditions
                var emergencyConditions = DetectAllEmergencyConditions(marketData);

                // Evaluate emergency signal
                var emergencySignal = EvaluateEmergencyConditions(emergencyConditions, marketData);

                stopwatch.Stop();

                var result = new EmergencyDetectionResult
                {
                    HasEmergency = emergencySignal != null,
                    EmergencySignal = emergencySignal,
                    DetectedConditions = emergencyConditions,
                    ProcessingTimeMs = stopwatch.Elapsed.TotalMilliseconds,
                    Timestamp = DateTime.UtcNow
                };

                if (result.HasEmergency)
                {
                    _lastEmergencySignal = DateTime.UtcNow;
                    _emergencyHistory.Add(emergencySignal!);
                    LogEmergencyDetection(result);
                }

                return result;
            }
        }

        /// <summary>
        /// Gets emergency detection statistics
        /// </summary>
        public EmergencyDetectionStatistics GetStatistics()
        {
            lock (_detectionLock)
            {
                var recentEmergencies = _emergencyHistory.ToArray();
                var last24Hours = DateTime.UtcNow.AddHours(-24);
                var recent24HourEmergencies = Array.FindAll(recentEmergencies, 
                    e => e.Timestamp > last24Hours);

                return new EmergencyDetectionStatistics
                {
                    TotalEmergencySignals = _emergencyHistory.Count,
                    EmergencySignalsLast24Hours = recent24HourEmergencies.Length,
                    LastEmergencySignal = _lastEmergencySignal,
                    BaselineEstablished = _baselineEstablished,
                    AverageProcessingTimeMs = CalculateAverageProcessingTime(),
                    MostCommonEmergencyType = GetMostCommonEmergencyType(recentEmergencies)
                };
            }
        }

        private void UpdateBaselines()
        {
            if (_marketHistory.Count < 20) return;

            var recentData = _marketHistory.ToArray();
            var dataCount = Math.Min(50, recentData.Length);
            var relevantData = recentData[^dataCount..];

            _baselineVolume = CalculateAverageVolume(relevantData);
            _baselineVolatility = CalculateVolatility(relevantData);
            _baselineVelocity = CalculateAverageVelocity(relevantData);
            _baselineEstablished = true;
        }

        private bool CanGenerateEmergencySignal()
        {
            var now = DateTime.UtcNow;
            
            // Check cooldown period
            if (now - _lastEmergencySignal < TimeSpan.FromSeconds(EmergencySignalCooldownSeconds))
            {
                return false;
            }

            // Check rate limiting
            var oneMinuteAgo = now.AddMinutes(-1);
            var recentEmergencies = _emergencyHistory.Where(e => e.Timestamp > oneMinuteAgo).Count();
            
            return recentEmergencies < MaxEmergencySignalsPerMinute;
        }

        private List<EmergencyCondition> DetectAllEmergencyConditions(MarketData marketData)
        {
            var conditions = new List<EmergencyCondition>();

            // 1. Explosive price velocity
            var velocityCondition = DetectExplosiveVelocity(marketData);
            if (velocityCondition != null) conditions.Add(velocityCondition);

            // 2. Massive volume spike
            var volumeCondition = DetectMassiveVolumeSpike(marketData);
            if (volumeCondition != null) conditions.Add(volumeCondition);

            // 3. Price gap detection
            var gapCondition = DetectPriceGap(marketData);
            if (gapCondition != null) conditions.Add(gapCondition);

            // 4. Velocity acceleration
            var accelerationCondition = DetectVelocityAcceleration(marketData);
            if (accelerationCondition != null) conditions.Add(accelerationCondition);

            // 5. Liquidity vacuum
            var liquidityCondition = DetectLiquidityVacuum(marketData);
            if (liquidityCondition != null) conditions.Add(liquidityCondition);

            return conditions;
        }

        private EmergencyCondition? DetectExplosiveVelocity(MarketData marketData)
        {
            var velocity = CalculatePriceVelocity(marketData);
            
            if (velocity > ExplosiveVelocityThreshold)
            {
                return new EmergencyCondition
                {
                    Type = EmergencyConditionType.ExplosiveVelocity,
                    Severity = CalculateVelocitySeverity(velocity),
                    Value = velocity,
                    Threshold = ExplosiveVelocityThreshold,
                    Description = $"Explosive price velocity: {velocity:P2} (threshold: {ExplosiveVelocityThreshold:P2})"
                };
            }

            return null;
        }

        private EmergencyCondition? DetectMassiveVolumeSpike(MarketData marketData)
        {
            if (!_baselineEstablished) return null;

            var volumeRatio = marketData.Volume / Math.Max(_baselineVolume, 1);
            
            if (volumeRatio > MassiveVolumeThreshold)
            {
                return new EmergencyCondition
                {
                    Type = EmergencyConditionType.MassiveVolumeSpike,
                    Severity = CalculateVolumeSeverity(volumeRatio),
                    Value = volumeRatio,
                    Threshold = MassiveVolumeThreshold,
                    Description = $"Massive volume spike: {volumeRatio:F1}x baseline (threshold: {MassiveVolumeThreshold:F1}x)"
                };
            }

            return null;
        }

        private EmergencyCondition? DetectPriceGap(MarketData marketData)
        {
            if (_marketHistory.Count < 2) return null;

            var previousData = _marketHistory[_marketHistory.Count - 2];
            var gap = (double)(Math.Abs(marketData.Price - previousData.Price) / previousData.Price);

            if (gap > PriceGapThreshold)
            {
                return new EmergencyCondition
                {
                    Type = EmergencyConditionType.PriceGap,
                    Severity = CalculateGapSeverity(gap),
                    Value = gap,
                    Threshold = PriceGapThreshold,
                    Description = $"Price gap: {gap:P2} (threshold: {PriceGapThreshold:P2})"
                };
            }

            return null;
        }

        private EmergencyCondition? DetectVelocityAcceleration(MarketData marketData)
        {
            if (!_baselineEstablished) return null;

            var currentVelocity = CalculatePriceVelocity(marketData);
            var acceleration = currentVelocity / Math.Max(_baselineVelocity, 0.0001);
            
            if (acceleration > VelocityAccelerationThreshold)
            {
                return new EmergencyCondition
                {
                    Type = EmergencyConditionType.VelocityAcceleration,
                    Severity = CalculateAccelerationSeverity(acceleration),
                    Value = acceleration,
                    Threshold = VelocityAccelerationThreshold,
                    Description = $"Velocity acceleration: {acceleration:F1}x baseline (threshold: {VelocityAccelerationThreshold:F1}x)"
                };
            }

            return null;
        }

        private EmergencyCondition? DetectLiquidityVacuum(MarketData marketData)
        {
            var rapidMovement = CalculateRapidMovement(marketData);
            
            if (rapidMovement > LiquidityVacuumThreshold)
            {
                return new EmergencyCondition
                {
                    Type = EmergencyConditionType.LiquidityVacuum,
                    Severity = CalculateLiquiditySeverity(rapidMovement),
                    Value = rapidMovement,
                    Threshold = LiquidityVacuumThreshold,
                    Description = $"Liquidity vacuum: {rapidMovement:P2} rapid movement (threshold: {LiquidityVacuumThreshold:P2})"
                };
            }

            return null;
        }

        private EmergencySignal? EvaluateEmergencyConditions(List<EmergencyCondition> conditions, MarketData marketData)
        {
            if (!conditions.Any()) return null;

            // Calculate overall emergency severity
            var totalSeverity = conditions.Sum(c => c.Severity);
            var averageSeverity = totalSeverity / conditions.Count;
            var maxSeverity = conditions.Max(c => c.Severity);

            // Determine if emergency signal should be generated
            var shouldGenerate = maxSeverity > 0.7 || (averageSeverity > 0.5 && conditions.Count >= 2);

            if (!shouldGenerate) return null;

            // Determine signal direction based on price movement
            var direction = DetermineEmergencyDirection(marketData);

            // Calculate emergency confidence
            var confidence = CalculateEmergencyConfidence(conditions, averageSeverity);

            return new EmergencySignal
            {
                Direction = direction,
                Confidence = confidence,
                Severity = maxSeverity,
                Conditions = conditions,
                Reason = CreateEmergencyReason(conditions),
                Timestamp = DateTime.UtcNow,
                MarketData = marketData
            };
        }

        // Helper calculation methods
        private double CalculatePriceVelocity(MarketData marketData)
        {
            // Calculate velocity using price vs mid price
            var midPrice = marketData.MidPrice;
            return (double)(Math.Abs(marketData.Price - midPrice) / Math.Max(midPrice, 0.0001m));
        }

        private double CalculateRapidMovement(MarketData marketData)
        {
            if (_marketHistory.Count < 3) return 0.0;

            var recentPrices = _marketHistory.ToArray().TakeLast(3).Select(d => d.Price).ToArray();
            var priceRange = recentPrices.Max() - recentPrices.Min();
            var avgPrice = recentPrices.Average();

            return (double)(priceRange / Math.Max(avgPrice, 0.0001m));
        }

        private double CalculateAverageVolume(MarketData[] data)
        {
            return data.Length > 0 ? data.Average(d => d.Volume) : 0;
        }

        private double CalculateVolatility(MarketData[] data)
        {
            if (data.Length < 2) return 0.0;

            var returns = new List<double>();
            for (int i = 1; i < data.Length; i++)
            {
                var ret = Math.Log((double)data[i].Price / (double)data[i - 1].Price);
                returns.Add(ret);
            }

            var mean = returns.Average();
            var variance = returns.Sum(r => Math.Pow(r - mean, 2)) / returns.Count;
            return Math.Sqrt(variance);
        }

        private double CalculateAverageVelocity(MarketData[] data)
        {
            return data.Length > 0 ? data.Average(d => (double)(Math.Abs(d.Price - d.MidPrice) / Math.Max(d.MidPrice, 0.0001m))) : 0;
        }

        // Severity calculation methods
        private double CalculateVelocitySeverity(double velocity) => Math.Min(velocity / (ExplosiveVelocityThreshold * 2), 1.0);
        private double CalculateVolumeSeverity(double ratio) => Math.Min((ratio - MassiveVolumeThreshold) / MassiveVolumeThreshold, 1.0);
        private double CalculateGapSeverity(double gap) => Math.Min(gap / (PriceGapThreshold * 2), 1.0);
        private double CalculateAccelerationSeverity(double acceleration) => Math.Min((acceleration - VelocityAccelerationThreshold) / VelocityAccelerationThreshold, 1.0);
        private double CalculateLiquiditySeverity(double movement) => Math.Min(movement / (LiquidityVacuumThreshold * 2), 1.0);

        private TradeDirection DetermineEmergencyDirection(MarketData marketData)
        {
            var priceVsBid = marketData.Price - marketData.Bid;
            var priceVsAsk = marketData.Ask - marketData.Price;
            return priceVsBid < priceVsAsk ? TradeDirection.Buy : TradeDirection.Sell;
        }

        private double CalculateEmergencyConfidence(List<EmergencyCondition> conditions, double averageSeverity)
        {
            var baseConfidence = 0.6; // Base emergency confidence
            var severityBoost = averageSeverity * 0.3; // Up to 30% boost from severity
            var conditionBoost = Math.Min(conditions.Count * 0.05, 0.2); // Up to 20% boost from multiple conditions
            
            return Math.Min(baseConfidence + severityBoost + conditionBoost, 0.95);
        }

        private string CreateEmergencyReason(List<EmergencyCondition> conditions)
        {
            var types = conditions.Select(c => c.Type.ToString()).ToArray();
            return $"Emergency: {string.Join(", ", types)}";
        }

        private EmergencyDetectionResult CreateNoEmergencyResult(string reason)
        {
            return new EmergencyDetectionResult
            {
                HasEmergency = false,
                EmergencySignal = null,
                DetectedConditions = new List<EmergencyCondition>(),
                ProcessingTimeMs = 0,
                Timestamp = DateTime.UtcNow,
                NoEmergencyReason = reason
            };
        }

        private void LogEmergencyDetection(EmergencyDetectionResult result)
        {
            if (result.HasEmergency && result.EmergencySignal != null)
            {
                _logger.LogWarning("🚨 EMERGENCY SIGNAL DETECTED: {Direction} - Confidence: {Confidence:P1} - {Reason}",
                    result.EmergencySignal.Direction,
                    result.EmergencySignal.Confidence,
                    result.EmergencySignal.Reason);

                foreach (var condition in result.DetectedConditions)
                {
                    _logger.LogWarning("   🔥 {Type}: {Description} (Severity: {Severity:P1})",
                        condition.Type, condition.Description, condition.Severity);
                }
            }
        }

        private double CalculateAverageProcessingTime()
        {
            // Placeholder - would track actual processing times
            return 1.5; // ms
        }

        private string GetMostCommonEmergencyType(EmergencySignal[] emergencies)
        {
            if (!emergencies.Any()) return "None";

            var typeCounts = emergencies
                .SelectMany(e => e.Conditions)
                .GroupBy(c => c.Type)
                .OrderByDescending(g => g.Count())
                .FirstOrDefault();

            return typeCounts?.Key.ToString() ?? "Unknown";
        }

        public void Dispose()
        {
            _marketHistory?.Dispose();
            _emergencyHistory?.Dispose();
        }
    }

    /// <summary>
    /// Types of emergency conditions that can be detected
    /// </summary>
    public enum EmergencyConditionType
    {
        ExplosiveVelocity,
        MassiveVolumeSpike,
        PriceGap,
        VelocityAcceleration,
        LiquidityVacuum
    }

    /// <summary>
    /// Individual emergency condition detected
    /// </summary>
    public class EmergencyCondition
    {
        public EmergencyConditionType Type { get; set; }
        public double Severity { get; set; }
        public double Value { get; set; }
        public double Threshold { get; set; }
        public string Description { get; set; } = string.Empty;
    }

    /// <summary>
    /// Emergency signal generated from detected conditions
    /// </summary>
    public class EmergencySignal
    {
        public TradeDirection Direction { get; set; }
        public double Confidence { get; set; }
        public double Severity { get; set; }
        public List<EmergencyCondition> Conditions { get; set; } = new();
        public string Reason { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public MarketData MarketData { get; set; } = new();
    }

    /// <summary>
    /// Result of emergency detection analysis
    /// </summary>
    public class EmergencyDetectionResult
    {
        public bool HasEmergency { get; set; }
        public EmergencySignal? EmergencySignal { get; set; }
        public List<EmergencyCondition> DetectedConditions { get; set; } = new();
        public double ProcessingTimeMs { get; set; }
        public DateTime Timestamp { get; set; }
        public string NoEmergencyReason { get; set; } = string.Empty;
    }

    /// <summary>
    /// Emergency detection performance statistics
    /// </summary>
    public class EmergencyDetectionStatistics
    {
        public int TotalEmergencySignals { get; set; }
        public int EmergencySignalsLast24Hours { get; set; }
        public DateTime LastEmergencySignal { get; set; }
        public bool BaselineEstablished { get; set; }
        public double AverageProcessingTimeMs { get; set; }
        public string MostCommonEmergencyType { get; set; } = string.Empty;
    }
}
