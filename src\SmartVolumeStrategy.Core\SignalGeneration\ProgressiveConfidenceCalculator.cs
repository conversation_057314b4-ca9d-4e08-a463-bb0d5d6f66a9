using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.MarketRegime;
using SmartVolumeStrategy.Core.Models;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SmartVolumeStrategy.Core.SignalGeneration
{
    /// <summary>
    /// Progressive confidence calculation system
    /// Replaces binary pass/fail with graduated confidence scoring
    /// </summary>
    public class ProgressiveConfidenceCalculator
    {
        private readonly ILogger<ProgressiveConfidenceCalculator> _logger;

        // Confidence component weights
        private const double BaseSignalWeight = 0.40;          // 40% from base signal
        private const double RegimeBoostWeight = 0.25;         // 25% from regime analysis
        private const double MomentumMultiplierWeight = 0.20;  // 20% from momentum detection
        private const double StabilityWeight = 0.15;           // 15% from regime stability

        // Momentum detection thresholds
        private const double MomentumVelocityThreshold = 0.005; // 0.5% price velocity
        private const double MomentumVolumeThreshold = 1.5;     // 1.5x volume spike
        private const double ExplosiveMomentumThreshold = 0.008; // 0.8% explosive threshold

        public ProgressiveConfidenceCalculator(ILogger<ProgressiveConfidenceCalculator> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Calculates progressive confidence using multiple factors
        /// </summary>
        public ProgressiveConfidenceResult CalculateConfidence(
            TradingSignal baseSignal,
            MarketRegimeAnalysis regimeAnalysis,
            MarketData marketData,
            List<object>? phase2Signals = null)
        {
            var components = new ConfidenceComponents();

            // 1. Base signal confidence (40%)
            components.BaseConfidence = baseSignal.Confidence * BaseSignalWeight;

            // 2. Regime-specific boost (25%)
            components.RegimeBoost = CalculateRegimeBoost(regimeAnalysis) * RegimeBoostWeight;

            // 3. Momentum multiplier (20%)
            components.MomentumMultiplier = CalculateMomentumMultiplier(marketData, regimeAnalysis) * MomentumMultiplierWeight;

            // 4. Stability factor (15%)
            components.StabilityFactor = CalculateStabilityFactor(regimeAnalysis) * StabilityWeight;

            // 5. Phase 2 enhancement (bonus)
            components.Phase2Enhancement = CalculatePhase2Enhancement(phase2Signals);

            // 6. Emergency momentum detection (potential override)
            components.EmergencyMomentumBoost = DetectEmergencyMomentum(marketData);

            // Calculate final confidence
            var finalConfidence = CalculateFinalConfidence(components);

            var result = new ProgressiveConfidenceResult
            {
                FinalConfidence = finalConfidence,
                Components = components,
                BaseSignal = baseSignal,
                RegimeAnalysis = regimeAnalysis,
                MarketData = marketData,
                CalculationReason = CreateCalculationReason(components),
                IsEmergencySignal = components.EmergencyMomentumBoost > 0,
                Timestamp = DateTime.UtcNow
            };

            LogConfidenceCalculation(result);
            return result;
        }

        /// <summary>
        /// Calculates regime-specific confidence boost
        /// </summary>
        private double CalculateRegimeBoost(MarketRegimeAnalysis regimeAnalysis)
        {
            var baseBoost = regimeAnalysis.PrimaryRegime switch
            {
                MarketRegime.Momentum => 0.30,    // 30% boost for momentum
                MarketRegime.Volatile => 0.15,    // 15% boost for volatile
                MarketRegime.Ranging => 0.05,     // 5% boost for ranging
                MarketRegime.Quiet => 0.02,       // 2% boost for quiet
                MarketRegime.Transition => 0.10,  // 10% boost for transition
                _ => 0.10
            };

            // Scale by regime strength
            var strengthMultiplier = Math.Max(0.3, regimeAnalysis.RegimeStrength);
            return baseBoost * strengthMultiplier;
        }

        /// <summary>
        /// Calculates momentum-based confidence multiplier
        /// </summary>
        private double CalculateMomentumMultiplier(MarketData marketData, MarketRegimeAnalysis regimeAnalysis)
        {
            // Calculate price velocity (rate of change)
            var priceVelocity = CalculatePriceVelocity(marketData);
            
            // Calculate volume spike factor
            var volumeSpike = CalculateVolumeSpike(marketData);

            // Base momentum score
            var momentumScore = 0.0;

            // Price velocity component
            if (priceVelocity > ExplosiveMomentumThreshold)
            {
                momentumScore += 0.40; // Explosive move
            }
            else if (priceVelocity > MomentumVelocityThreshold)
            {
                momentumScore += 0.25; // Strong move
            }

            // Volume spike component
            if (volumeSpike > MomentumVolumeThreshold)
            {
                momentumScore += 0.20; // Volume confirmation
            }

            // Trend alignment component
            if (regimeAnalysis.TrendProfile.TrendStrength > 0.7)
            {
                momentumScore += 0.15; // Strong trend alignment
            }

            // Multi-timeframe alignment component
            if (regimeAnalysis.TrendProfile.MultiTimeframeAlignment > 0.8)
            {
                momentumScore += 0.10; // Multi-timeframe confirmation
            }

            return Math.Min(momentumScore, 0.50); // Cap at 50%
        }

        /// <summary>
        /// Calculates stability-based confidence factor
        /// </summary>
        private double CalculateStabilityFactor(MarketRegimeAnalysis regimeAnalysis)
        {
            var stability = regimeAnalysis.RegimeStability;
            
            // High stability = higher confidence
            // Low stability = lower confidence (but not zero)
            return Math.Max(0.3, stability); // Minimum 30% factor
        }

        /// <summary>
        /// Calculates Phase 2 signal enhancement
        /// </summary>
        private double CalculatePhase2Enhancement(List<object>? phase2Signals)
        {
            if (phase2Signals == null || !phase2Signals.Any())
                return 0.0;

            // Count valid Phase 2 signals
            var validSignals = phase2Signals.Count(s => s != null);
            var totalSignals = phase2Signals.Count;

            if (totalSignals == 0) return 0.0;

            // Enhancement based on Phase 2 signal quality
            var phase2Quality = (double)validSignals / totalSignals;
            return phase2Quality * 0.15; // Up to 15% enhancement
        }

        /// <summary>
        /// Detects emergency momentum conditions
        /// </summary>
        private double DetectEmergencyMomentum(MarketData marketData)
        {
            var priceVelocity = CalculatePriceVelocity(marketData);
            var volumeSpike = CalculateVolumeSpike(marketData);

            // Emergency conditions
            var isExplosiveMove = priceVelocity > ExplosiveMomentumThreshold;
            var isMassiveVolume = volumeSpike > 3.0; // 3x volume spike
            var isPriceGap = CalculatePriceGap(marketData) > 0.01; // 1% gap

            if (isExplosiveMove && isMassiveVolume)
            {
                return 0.30; // 30% emergency boost
            }
            else if (isExplosiveMove || (isMassiveVolume && isPriceGap))
            {
                return 0.20; // 20% emergency boost
            }
            else if (isMassiveVolume || isPriceGap)
            {
                return 0.10; // 10% emergency boost
            }

            return 0.0; // No emergency conditions
        }

        /// <summary>
        /// Calculates final confidence from all components
        /// </summary>
        private double CalculateFinalConfidence(ConfidenceComponents components)
        {
            // Base calculation
            var baseConfidence = components.BaseConfidence + 
                               components.RegimeBoost + 
                               components.MomentumMultiplier + 
                               components.StabilityFactor;

            // Add enhancements
            var enhancedConfidence = baseConfidence + components.Phase2Enhancement;

            // Apply emergency boost if present
            if (components.EmergencyMomentumBoost > 0)
            {
                enhancedConfidence = Math.Max(enhancedConfidence, 
                    baseConfidence + components.EmergencyMomentumBoost);
            }

            // Ensure confidence is within valid range
            return Math.Max(0.0, Math.Min(1.0, enhancedConfidence));
        }

        /// <summary>
        /// Helper method to calculate price velocity
        /// </summary>
        private double CalculatePriceVelocity(MarketData marketData)
        {
            // Simple velocity calculation - would be more sophisticated with historical data
            var priceChange = Math.Abs(marketData.Close - marketData.Open);
            var velocity = priceChange / marketData.Open;
            return velocity;
        }

        /// <summary>
        /// Helper method to calculate volume spike
        /// </summary>
        private double CalculateVolumeSpike(MarketData marketData)
        {
            // Simplified volume spike calculation
            // In practice, this would compare against historical average
            var baselineVolume = 50000; // Placeholder - would be calculated from history
            return (double)marketData.Volume / baselineVolume;
        }

        /// <summary>
        /// Helper method to calculate price gap
        /// </summary>
        private double CalculatePriceGap(MarketData marketData)
        {
            // Simplified gap calculation
            // In practice, this would compare against previous close
            var previousClose = marketData.Open; // Placeholder
            return Math.Abs(marketData.Open - previousClose) / previousClose;
        }

        /// <summary>
        /// Creates human-readable calculation reason
        /// </summary>
        private string CreateCalculationReason(ConfidenceComponents components)
        {
            var reasons = new List<string>();

            if (components.BaseConfidence > 0.25)
                reasons.Add($"Strong base signal ({components.BaseConfidence:P1})");

            if (components.RegimeBoost > 0.15)
                reasons.Add($"Favorable regime ({components.RegimeBoost:P1})");

            if (components.MomentumMultiplier > 0.10)
                reasons.Add($"Momentum detected ({components.MomentumMultiplier:P1})");

            if (components.Phase2Enhancement > 0.05)
                reasons.Add($"Phase 2 confirmation ({components.Phase2Enhancement:P1})");

            if (components.EmergencyMomentumBoost > 0)
                reasons.Add($"Emergency momentum ({components.EmergencyMomentumBoost:P1})");

            return reasons.Any() ? string.Join(", ", reasons) : "Standard calculation";
        }

        /// <summary>
        /// Logs confidence calculation details
        /// </summary>
        private void LogConfidenceCalculation(ProgressiveConfidenceResult result)
        {
            if (result.IsEmergencySignal)
            {
                _logger.LogWarning("🚨 EMERGENCY CONFIDENCE: {FinalConfidence:P1} - {Reason}",
                    result.FinalConfidence, result.CalculationReason);
            }
            else if (result.FinalConfidence > 0.8)
            {
                _logger.LogInformation("🎯 HIGH CONFIDENCE: {FinalConfidence:P1} - {Reason}",
                    result.FinalConfidence, result.CalculationReason);
            }
            else
            {
                _logger.LogDebug("📊 Confidence calculated: {FinalConfidence:P1} - {Reason}",
                    result.FinalConfidence, result.CalculationReason);
            }
        }
    }

    /// <summary>
    /// Individual confidence calculation components
    /// </summary>
    public class ConfidenceComponents
    {
        /// <summary>
        /// Base signal confidence component
        /// </summary>
        public double BaseConfidence { get; set; }

        /// <summary>
        /// Regime-specific boost component
        /// </summary>
        public double RegimeBoost { get; set; }

        /// <summary>
        /// Momentum multiplier component
        /// </summary>
        public double MomentumMultiplier { get; set; }

        /// <summary>
        /// Stability factor component
        /// </summary>
        public double StabilityFactor { get; set; }

        /// <summary>
        /// Phase 2 enhancement component
        /// </summary>
        public double Phase2Enhancement { get; set; }

        /// <summary>
        /// Emergency momentum boost component
        /// </summary>
        public double EmergencyMomentumBoost { get; set; }
    }

    /// <summary>
    /// Result of progressive confidence calculation
    /// </summary>
    public class ProgressiveConfidenceResult
    {
        /// <summary>
        /// Final calculated confidence (0.0 to 1.0)
        /// </summary>
        public double FinalConfidence { get; set; }

        /// <summary>
        /// Breakdown of confidence components
        /// </summary>
        public ConfidenceComponents Components { get; set; } = new();

        /// <summary>
        /// Original base signal
        /// </summary>
        public TradingSignal BaseSignal { get; set; } = new();

        /// <summary>
        /// Market regime analysis used
        /// </summary>
        public MarketRegimeAnalysis RegimeAnalysis { get; set; } = new();

        /// <summary>
        /// Market data used for calculation
        /// </summary>
        public MarketData MarketData { get; set; } = new();

        /// <summary>
        /// Human-readable reason for the confidence level
        /// </summary>
        public string CalculationReason { get; set; } = string.Empty;

        /// <summary>
        /// Whether this was triggered by emergency conditions
        /// </summary>
        public bool IsEmergencySignal { get; set; }

        /// <summary>
        /// When this calculation was performed
        /// </summary>
        public DateTime Timestamp { get; set; }
    }
}
