using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Models;
using SmartVolumeStrategy.Core.Utils;
using System;
using System.Collections.Generic;
using System.Linq;

namespace SmartVolumeStrategy.Core.Regime
{
    /// <summary>
    /// Advanced market regime detection system
    /// Analyzes market conditions to determine optimal strategy behavior
    /// </summary>
    public class MarketRegimeDetector : IDisposable
    {
        private readonly ILogger<MarketRegimeDetector> _logger;
        private readonly CircularBuffer<MarketData> _marketHistory;
        private readonly CircularBuffer<MarketRegimeAnalysis> _regimeHistory;
        private readonly object _detectionLock = new();

        // Configuration
        private const int MarketHistorySize = 200;
        private const int RegimeHistorySize = 50;
        private const int MinDataPointsForAnalysis = 20;

        // Regime detection parameters
        private const double MomentumTrendThreshold = 0.7;
        private const double MomentumVolumeThreshold = 1.5;
        private const double RangingTrendThreshold = 0.3;
        private const double VolatileVolatilityThreshold = 2.0;
        private const double QuietVolumeThreshold = 0.7;
        private const double QuietVolatilityThreshold = 0.5;

        // Baseline calculations
        private double _baselineVolatility;
        private double _baselineVolume;
        private double _baselinePrice;
        private bool _baselineEstablished;

        public MarketRegimeDetector(ILogger<MarketRegimeDetector> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _marketHistory = new CircularBuffer<MarketData>(MarketHistorySize);
            _regimeHistory = new CircularBuffer<MarketRegimeAnalysis>(RegimeHistorySize);

            _logger.LogInformation("🧠 Market Regime Detector initialized");
        }

        /// <summary>
        /// Analyzes current market conditions and determines regime
        /// </summary>
        public MarketRegimeAnalysis AnalyzeMarketRegime(MarketData marketData)
        {
            lock (_detectionLock)
            {
                // Add to history
                _marketHistory.Add(marketData);

                // Ensure we have enough data
                if (_marketHistory.Count < MinDataPointsForAnalysis)
                {
                    return CreateInitialAnalysis(marketData);
                }

                // Update baselines if needed
                UpdateBaselines();

                // Perform comprehensive analysis
                var analysis = PerformRegimeAnalysis(marketData);

                // Add to regime history
                _regimeHistory.Add(analysis);

                // Log regime changes
                LogRegimeAnalysis(analysis);

                return analysis;
            }
        }

        /// <summary>
        /// Gets the current regime stability trend
        /// </summary>
        public double GetRegimeStabilityTrend()
        {
            lock (_detectionLock)
            {
                if (_regimeHistory.Count < 5) return 0.5; // Neutral

                var recentRegimes = _regimeHistory.ToArray().TakeLast(5).ToArray();
                var primaryRegime = recentRegimes.Last().PrimaryRegime;
                var consistencyCount = recentRegimes.Count(r => r.PrimaryRegime == primaryRegime);

                return (double)consistencyCount / recentRegimes.Length;
            }
        }

        /// <summary>
        /// Validates regime across multiple timeframes
        /// </summary>
        public RegimeValidationResult ValidateRegimeAcrossTimeframes(MarketData marketData)
        {
            var timeframes = new[] 
            { 
                TimeSpan.FromSeconds(15), 
                TimeSpan.FromSeconds(30), 
                TimeSpan.FromMinutes(1) 
            };

            var regimeVotes = new Dictionary<MarketRegime, int>();
            var timeframeBreakdown = new Dictionary<TimeSpan, MarketRegime>();

            foreach (var timeframe in timeframes)
            {
                var regime = AnalyzeRegimeForTimeframe(marketData, timeframe);
                regimeVotes[regime] = regimeVotes.GetValueOrDefault(regime, 0) + 1;
                timeframeBreakdown[timeframe] = regime;
            }

            var consensusRegime = regimeVotes.OrderByDescending(kvp => kvp.Value).First();
            var consensusStrength = (double)consensusRegime.Value / timeframes.Length;

            return new RegimeValidationResult
            {
                ConsensusRegime = consensusRegime.Key,
                ConsensusStrength = consensusStrength,
                IsStrongConsensus = consensusStrength >= 0.67,
                ConflictingRegimes = regimeVotes.Where(kvp => kvp.Key != consensusRegime.Key).ToList(),
                TimeframeBreakdown = timeframeBreakdown
            };
        }

        private MarketRegimeAnalysis CreateInitialAnalysis(MarketData marketData)
        {
            return new MarketRegimeAnalysis
            {
                PrimaryRegime = Regime.MarketRegime.Transition,
                RegimeStrength = 0.5,
                RegimeStability = 0.5,
                TransitionProbability = 0.8,
                Timestamp = marketData.Timestamp,
                VolatilityProfile = new VolatilityProfile { RelativeVolatility = 1.0 },
                TrendProfile = new TrendProfile { TrendStrength = 0.0 },
                VolumeProfile = new VolumeProfile { RelativeVolume = 1.0 },
                Metadata = new Dictionary<string, object>
                {
                    ["Reason"] = "Insufficient data for analysis",
                    ["DataPoints"] = _marketHistory.Count
                }
            };
        }

        private void UpdateBaselines()
        {
            if (_marketHistory.Count < MinDataPointsForAnalysis) return;

            var recentData = _marketHistory.ToArray().TakeLast(50).ToArray();

            _baselineVolatility = CalculateVolatility(recentData);
            _baselineVolume = recentData.Average(d => d.Volume);
            _baselinePrice = (double)recentData.Average(d => d.Price);
            _baselineEstablished = true;
        }

        private MarketRegimeAnalysis PerformRegimeAnalysis(MarketData marketData)
        {
            var recentData = _marketHistory.ToArray().TakeLast(50).ToArray();

            // Calculate core metrics
            var volatilityProfile = AnalyzeVolatilityProfile(recentData, marketData);
            var trendProfile = AnalyzeTrendProfile(recentData, marketData);
            var volumeProfile = AnalyzeVolumeProfile(recentData, marketData);

            // Determine primary regime
            var primaryRegime = ClassifyRegime(volatilityProfile, trendProfile, volumeProfile);
            var regimeStrength = CalculateRegimeStrength(primaryRegime, volatilityProfile, trendProfile, volumeProfile);
            var regimeStability = CalculateRegimeStability(primaryRegime);
            var transitionProbability = CalculateTransitionProbability(primaryRegime);

            return new MarketRegimeAnalysis
            {
                PrimaryRegime = primaryRegime,
                RegimeStrength = regimeStrength,
                RegimeStability = regimeStability,
                TransitionProbability = transitionProbability,
                VolatilityProfile = volatilityProfile,
                TrendProfile = trendProfile,
                VolumeProfile = volumeProfile,
                Timestamp = marketData.Timestamp,
                Metadata = CreateAnalysisMetadata(primaryRegime, regimeStrength)
            };
        }

        private Regime.MarketRegime ClassifyRegime(VolatilityProfile volatility, TrendProfile trend, VolumeProfile volume)
        {
            // Momentum: Strong trend + High volume + Moderate volatility
            if (trend.TrendStrength > MomentumTrendThreshold &&
                volume.RelativeVolume > MomentumVolumeThreshold &&
                volatility.RelativeVolatility < VolatileVolatilityThreshold)
            {
                return Regime.MarketRegime.Momentum;
            }

            // Volatile: High volatility regardless of other factors
            if (volatility.RelativeVolatility > VolatileVolatilityThreshold)
            {
                return Regime.MarketRegime.Volatile;
            }

            // Quiet: Low volume + Low volatility
            if (volume.RelativeVolume < QuietVolumeThreshold &&
                volatility.RelativeVolatility < QuietVolatilityThreshold)
            {
                return Regime.MarketRegime.Quiet;
            }

            // Ranging: Low trend strength + Normal volume
            if (trend.TrendStrength < RangingTrendThreshold)
            {
                return Regime.MarketRegime.Ranging;
            }

            // Default to transition if no clear regime
            return Regime.MarketRegime.Transition;
        }

        private VolatilityProfile AnalyzeVolatilityProfile(MarketData[] recentData, MarketData currentData)
        {
            var currentVolatility = CalculateVolatility(recentData);
            var relativeVolatility = _baselineEstablished ? currentVolatility / Math.Max(_baselineVolatility, 0.0001) : 1.0;

            return new VolatilityProfile
            {
                RelativeVolatility = relativeVolatility,
                VolatilityTrend = CalculateVolatilityTrend(recentData),
                ClusteringFactor = CalculateVolatilityClustering(recentData),
                IntradayPattern = CalculateIntradayVolatilityPattern(recentData)
            };
        }

        private TrendProfile AnalyzeTrendProfile(MarketData[] recentData, MarketData currentData)
        {
            var trendStrength = CalculateTrendStrength(recentData);
            var trendDirection = CalculateTrendDirection(recentData);

            return new TrendProfile
            {
                TrendStrength = trendStrength,
                TrendDirection = trendDirection,
                TrendConsistency = CalculateTrendConsistency(recentData),
                TrendAcceleration = CalculateTrendAcceleration(recentData),
                MultiTimeframeAlignment = CalculateMultiTimeframeAlignment(recentData)
            };
        }

        private VolumeProfile AnalyzeVolumeProfile(MarketData[] recentData, MarketData currentData)
        {
            var relativeVolume = _baselineEstablished ? currentData.Volume / Math.Max(_baselineVolume, 1) : 1.0;

            return new VolumeProfile
            {
                RelativeVolume = relativeVolume,
                VolumeTrend = CalculateVolumeTrend(recentData),
                VolumePriceCorrelation = CalculateVolumePriceCorrelation(recentData),
                VolumeDistribution = CalculateVolumeDistribution(recentData),
                InstitutionalVolumeSignal = CalculateInstitutionalVolumeSignal(recentData)
            };
        }

        // Helper calculation methods
        private double CalculateVolatility(MarketData[] data)
        {
            if (data.Length < 2) return 0.0;

            var returns = new List<double>();
            for (int i = 1; i < data.Length; i++)
            {
                var ret = Math.Log((double)data[i].Price / (double)data[i - 1].Price);
                returns.Add(ret);
            }

            var mean = returns.Average();
            var variance = returns.Sum(r => Math.Pow(r - mean, 2)) / returns.Count;
            return Math.Sqrt(variance);
        }

        private double CalculateTrendStrength(MarketData[] data)
        {
            if (data.Length < 10) return 0.0;

            var prices = data.Select(d => (double)d.Price).ToArray();
            var n = prices.Length;
            var sumX = n * (n - 1) / 2.0;
            var sumY = prices.Sum();
            var sumXY = prices.Select((price, index) => price * index).Sum();
            var sumX2 = n * (n - 1) * (2 * n - 1) / 6.0;

            var slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
            var avgPrice = sumY / n;

            return Math.Min(Math.Abs(slope) / avgPrice * 1000, 1.0); // Normalize to 0-1
        }

        private double CalculateTrendDirection(MarketData[] data)
        {
            if (data.Length < 2) return 0.0;

            var firstPrice = data.First().Price;
            var lastPrice = data.Last().Price;
            var change = (double)((lastPrice - firstPrice) / firstPrice);

            return Math.Max(-1.0, Math.Min(1.0, change * 10)); // Normalize to -1 to 1
        }

        // Additional helper methods would be implemented here...
        private double CalculateVolatilityTrend(MarketData[] data) => 0.0; // Placeholder
        private double CalculateVolatilityClustering(MarketData[] data) => 0.0; // Placeholder
        private double CalculateIntradayVolatilityPattern(MarketData[] data) => 0.0; // Placeholder
        private double CalculateTrendConsistency(MarketData[] data) => 0.8; // Placeholder
        private double CalculateTrendAcceleration(MarketData[] data) => 0.0; // Placeholder
        private double CalculateMultiTimeframeAlignment(MarketData[] data) => 0.7; // Placeholder
        private double CalculateVolumeTrend(MarketData[] data) => 0.0; // Placeholder
        private double CalculateVolumePriceCorrelation(MarketData[] data) => 0.5; // Placeholder
        private double CalculateVolumeDistribution(MarketData[] data) => 0.5; // Placeholder
        private double CalculateInstitutionalVolumeSignal(MarketData[] data) => 0.3; // Placeholder

        private double CalculateRegimeStrength(Regime.MarketRegime regime, VolatilityProfile vol, TrendProfile trend, VolumeProfile volume)
        {
            return regime switch
            {
                Regime.MarketRegime.Momentum => Math.Min(trend.TrendStrength + volume.RelativeVolume * 0.3, 1.0),
                Regime.MarketRegime.Volatile => Math.Min(vol.RelativeVolatility * 0.5, 1.0),
                Regime.MarketRegime.Ranging => Math.Min((1.0 - trend.TrendStrength) * 1.2, 1.0),
                Regime.MarketRegime.Quiet => Math.Min((2.0 - volume.RelativeVolume - vol.RelativeVolatility) * 0.5, 1.0),
                _ => 0.5
            };
        }

        private double CalculateRegimeStability(Regime.MarketRegime regime)
        {
            if (_regimeHistory.Count < 3) return 0.5;

            var recentRegimes = _regimeHistory.ToArray().TakeLast(3).Select(r => r.PrimaryRegime).ToArray();
            var consistencyCount = recentRegimes.Count(r => r == regime);
            return (double)consistencyCount / recentRegimes.Length;
        }

        private double CalculateTransitionProbability(Regime.MarketRegime regime)
        {
            var stability = CalculateRegimeStability(regime);
            return 1.0 - stability; // Higher stability = lower transition probability
        }

        private Regime.MarketRegime AnalyzeRegimeForTimeframe(MarketData marketData, TimeSpan timeframe)
        {
            // Simplified timeframe analysis - would be more sophisticated in practice
            var analysis = AnalyzeMarketRegime(marketData);
            return analysis.PrimaryRegime;
        }

        private Dictionary<string, object> CreateAnalysisMetadata(Regime.MarketRegime regime, double strength)
        {
            return new Dictionary<string, object>
            {
                ["DetectionMethod"] = "MultiFactorAnalysis",
                ["DataPoints"] = _marketHistory.Count,
                ["BaselineEstablished"] = _baselineEstablished,
                ["RegimeConfidence"] = strength,
                ["AnalysisVersion"] = "1.0"
            };
        }

        private void LogRegimeAnalysis(MarketRegimeAnalysis analysis)
        {
            var previousRegime = _regimeHistory.Count > 0 ? _regimeHistory.LastOrDefault()?.PrimaryRegime : null;
            
            if (previousRegime != analysis.PrimaryRegime)
            {
                _logger.LogInformation("🔄 REGIME CHANGE: {PreviousRegime} → {NewRegime} (Strength: {Strength:P1}, Stability: {Stability:P1})",
                    previousRegime?.ToString() ?? "Unknown", 
                    analysis.PrimaryRegime, 
                    analysis.RegimeStrength, 
                    analysis.RegimeStability);
            }
        }

        public void Dispose()
        {
            _marketHistory?.Dispose();
            _regimeHistory?.Dispose();
        }
    }
}
