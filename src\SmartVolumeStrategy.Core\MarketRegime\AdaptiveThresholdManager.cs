using Microsoft.Extensions.Logging;
using SmartVolumeStrategy.Core.Models;
using System;
using System.Collections.Generic;

namespace SmartVolumeStrategy.Core.MarketRegime
{
    /// <summary>
    /// Adaptive threshold management system
    /// Dynamically adjusts strategy parameters based on market regime
    /// </summary>
    public class AdaptiveThresholdManager
    {
        private readonly ILogger<AdaptiveThresholdManager> _logger;
        private readonly Dictionary<MarketRegime, RegimeConfiguration> _regimeConfigurations;
        private readonly object _configLock = new();

        // Performance tracking
        private long _thresholdAdjustments;
        private DateTime _lastAdjustment;

        public AdaptiveThresholdManager(ILogger<AdaptiveThresholdManager> logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _regimeConfigurations = InitializeDefaultConfigurations();
            
            _logger.LogInformation("🎯 Adaptive Threshold Manager initialized with {RegimeCount} regime configurations", 
                _regimeConfigurations.Count);
        }

        /// <summary>
        /// Gets adaptive thresholds for the specified market regime
        /// </summary>
        public RegimeConfiguration GetThresholds(MarketRegime regime)
        {
            lock (_configLock)
            {
                if (_regimeConfigurations.TryGetValue(regime, out var config))
                {
                    _logger.LogDebug("📊 Retrieved thresholds for {Regime}: Confidence={Confidence:P1}, Alignment={Alignment}",
                        regime, config.ConfidenceThreshold, config.IndicatorAlignment);
                    return config;
                }

                // Fallback to default configuration
                _logger.LogWarning("⚠️ No configuration found for regime {Regime}, using default", regime);
                return _regimeConfigurations[MarketRegime.Transition];
            }
        }

        /// <summary>
        /// Gets adaptive thresholds with regime analysis context
        /// </summary>
        public AdaptiveThresholdResult GetAdaptiveThresholds(MarketRegimeAnalysis regimeAnalysis)
        {
            lock (_configLock)
            {
                var baseConfig = GetThresholds(regimeAnalysis.PrimaryRegime);
                var adjustedConfig = ApplyRegimeStrengthAdjustments(baseConfig, regimeAnalysis);
                var finalConfig = ApplyStabilityAdjustments(adjustedConfig, regimeAnalysis);

                _thresholdAdjustments++;
                _lastAdjustment = DateTime.UtcNow;

                return new AdaptiveThresholdResult
                {
                    Configuration = finalConfig,
                    BaseConfiguration = baseConfig,
                    RegimeAnalysis = regimeAnalysis,
                    AdjustmentReason = CreateAdjustmentReason(regimeAnalysis),
                    Timestamp = DateTime.UtcNow
                };
            }
        }

        /// <summary>
        /// Updates configuration for a specific regime
        /// </summary>
        public void UpdateRegimeConfiguration(MarketRegime regime, RegimeConfiguration configuration)
        {
            lock (_configLock)
            {
                _regimeConfigurations[regime] = configuration;
                _logger.LogInformation("🔧 Updated configuration for {Regime}: Confidence={Confidence:P1}, Alignment={Alignment}",
                    regime, configuration.ConfidenceThreshold, configuration.IndicatorAlignment);
            }
        }

        /// <summary>
        /// Gets performance statistics
        /// </summary>
        public AdaptiveThresholdStatistics GetStatistics()
        {
            lock (_configLock)
            {
                return new AdaptiveThresholdStatistics
                {
                    TotalAdjustments = _thresholdAdjustments,
                    LastAdjustment = _lastAdjustment,
                    ConfiguredRegimes = _regimeConfigurations.Count,
                    AverageConfidenceThreshold = CalculateAverageConfidenceThreshold(),
                    AverageIndicatorAlignment = CalculateAverageIndicatorAlignment()
                };
            }
        }

        private Dictionary<MarketRegime, RegimeConfiguration> InitializeDefaultConfigurations()
        {
            return new Dictionary<MarketRegime, RegimeConfiguration>
            {
                [MarketRegime.Momentum] = new RegimeConfiguration
                {
                    Regime = MarketRegime.Momentum,
                    ConfidenceThreshold = 0.35,        // Ultra-aggressive for momentum
                    IndicatorAlignment = 1,            // Single indicator sufficient
                    VolumeThreshold = 1000,            // Low volume requirement
                    SignalIntervalSeconds = 2,         // Very fast signals
                    MaxSignalsPerHour = 100,           // High frequency
                    RiskMultiplier = 0.8,              // Lower risk threshold
                    UrgencyMultiplier = 2.0,           // High urgency
                    AllowEmergencyBypass = true        // Allow emergency signals
                },

                [MarketRegime.Volatile] = new RegimeConfiguration
                {
                    Regime = MarketRegime.Volatile,
                    ConfidenceThreshold = 0.50,        // Medium threshold
                    IndicatorAlignment = 2,            // Moderate confirmation
                    VolumeThreshold = 2000,            // Medium volume requirement
                    SignalIntervalSeconds = 5,         // Medium speed
                    MaxSignalsPerHour = 60,            // Medium frequency
                    RiskMultiplier = 1.0,              // Normal risk
                    UrgencyMultiplier = 1.5,           // Medium-high urgency
                    AllowEmergencyBypass = true        // Allow emergency signals
                },

                [MarketRegime.Ranging] = new RegimeConfiguration
                {
                    Regime = MarketRegime.Ranging,
                    ConfidenceThreshold = 0.65,        // Conservative threshold
                    IndicatorAlignment = 3,            // Multiple confirmations
                    VolumeThreshold = 3000,            // Higher volume requirement
                    SignalIntervalSeconds = 15,        // Slower signals
                    MaxSignalsPerHour = 30,            // Lower frequency
                    RiskMultiplier = 1.2,              // Higher risk threshold
                    UrgencyMultiplier = 0.5,           // Low urgency
                    AllowEmergencyBypass = false       // No emergency bypasses
                },

                [MarketRegime.Quiet] = new RegimeConfiguration
                {
                    Regime = MarketRegime.Quiet,
                    ConfidenceThreshold = 0.70,        // High threshold
                    IndicatorAlignment = 3,            // Full confirmation required
                    VolumeThreshold = 5000,            // High volume requirement
                    SignalIntervalSeconds = 30,        // Very slow signals
                    MaxSignalsPerHour = 15,            // Very low frequency
                    RiskMultiplier = 1.5,              // Much higher risk threshold
                    UrgencyMultiplier = 0.3,           // Very low urgency
                    AllowEmergencyBypass = false       // No emergency bypasses
                },

                [MarketRegime.Transition] = new RegimeConfiguration
                {
                    Regime = MarketRegime.Transition,
                    ConfidenceThreshold = 0.60,        // Medium-high threshold
                    IndicatorAlignment = 2,            // Moderate confirmation
                    VolumeThreshold = 2500,            // Medium-high volume
                    SignalIntervalSeconds = 10,        // Medium speed
                    MaxSignalsPerHour = 40,            // Medium frequency
                    RiskMultiplier = 1.1,              // Slightly higher risk threshold
                    UrgencyMultiplier = 0.8,           // Medium-low urgency
                    AllowEmergencyBypass = false       // No emergency bypasses during transition
                }
            };
        }

        private RegimeConfiguration ApplyRegimeStrengthAdjustments(
            RegimeConfiguration baseConfig, 
            MarketRegimeAnalysis regimeAnalysis)
        {
            var adjustedConfig = CloneConfiguration(baseConfig);
            var strengthFactor = regimeAnalysis.RegimeStrength;

            // Stronger regime = more aggressive thresholds
            switch (regimeAnalysis.PrimaryRegime)
            {
                case MarketRegime.Momentum:
                    // For momentum, stronger regime means even more aggressive
                    adjustedConfig.ConfidenceThreshold *= (1.0 - strengthFactor * 0.3); // Up to 30% reduction
                    adjustedConfig.SignalIntervalSeconds = Math.Max(1, 
                        (int)(adjustedConfig.SignalIntervalSeconds * (1.0 - strengthFactor * 0.5)));
                    break;

                case MarketRegime.Volatile:
                    // For volatile, stronger regime means more cautious
                    adjustedConfig.ConfidenceThreshold *= (1.0 + strengthFactor * 0.2); // Up to 20% increase
                    adjustedConfig.IndicatorAlignment = Math.Min(3, 
                        adjustedConfig.IndicatorAlignment + (int)(strengthFactor * 1));
                    break;

                case MarketRegime.Ranging:
                    // For ranging, stronger regime means more conservative
                    adjustedConfig.ConfidenceThreshold *= (1.0 + strengthFactor * 0.15); // Up to 15% increase
                    break;

                case MarketRegime.Quiet:
                    // For quiet, stronger regime means much more conservative
                    adjustedConfig.ConfidenceThreshold *= (1.0 + strengthFactor * 0.25); // Up to 25% increase
                    adjustedConfig.VolumeThreshold *= (1.0 + strengthFactor * 0.5); // Up to 50% increase
                    break;
            }

            return adjustedConfig;
        }

        private RegimeConfiguration ApplyStabilityAdjustments(
            RegimeConfiguration baseConfig, 
            MarketRegimeAnalysis regimeAnalysis)
        {
            var adjustedConfig = CloneConfiguration(baseConfig);
            var stabilityFactor = regimeAnalysis.RegimeStability;

            // More stable regime = more confident in thresholds
            // Less stable regime = more conservative thresholds

            if (stabilityFactor < 0.5) // Unstable regime
            {
                adjustedConfig.ConfidenceThreshold *= 1.2; // 20% higher threshold
                adjustedConfig.IndicatorAlignment = Math.Min(3, adjustedConfig.IndicatorAlignment + 1);
                adjustedConfig.SignalIntervalSeconds = (int)(adjustedConfig.SignalIntervalSeconds * 1.5);
            }
            else if (stabilityFactor > 0.8) // Very stable regime
            {
                adjustedConfig.ConfidenceThreshold *= 0.9; // 10% lower threshold
                adjustedConfig.SignalIntervalSeconds = Math.Max(1, 
                    (int)(adjustedConfig.SignalIntervalSeconds * 0.8));
            }

            return adjustedConfig;
        }

        private RegimeConfiguration CloneConfiguration(RegimeConfiguration original)
        {
            return new RegimeConfiguration
            {
                Regime = original.Regime,
                ConfidenceThreshold = original.ConfidenceThreshold,
                IndicatorAlignment = original.IndicatorAlignment,
                VolumeThreshold = original.VolumeThreshold,
                SignalIntervalSeconds = original.SignalIntervalSeconds,
                MaxSignalsPerHour = original.MaxSignalsPerHour,
                RiskMultiplier = original.RiskMultiplier,
                UrgencyMultiplier = original.UrgencyMultiplier,
                AllowEmergencyBypass = original.AllowEmergencyBypass
            };
        }

        private string CreateAdjustmentReason(MarketRegimeAnalysis regimeAnalysis)
        {
            var reasons = new List<string>();

            if (regimeAnalysis.RegimeStrength > 0.8)
                reasons.Add($"Strong {regimeAnalysis.PrimaryRegime} regime");
            else if (regimeAnalysis.RegimeStrength < 0.4)
                reasons.Add($"Weak {regimeAnalysis.PrimaryRegime} regime");

            if (regimeAnalysis.RegimeStability < 0.5)
                reasons.Add("Low stability");
            else if (regimeAnalysis.RegimeStability > 0.8)
                reasons.Add("High stability");

            if (regimeAnalysis.TransitionProbability > 0.7)
                reasons.Add("High transition probability");

            return reasons.Count > 0 ? string.Join(", ", reasons) : "Standard adjustment";
        }

        private double CalculateAverageConfidenceThreshold()
        {
            var sum = 0.0;
            foreach (var config in _regimeConfigurations.Values)
            {
                sum += config.ConfidenceThreshold;
            }
            return sum / _regimeConfigurations.Count;
        }

        private double CalculateAverageIndicatorAlignment()
        {
            var sum = 0.0;
            foreach (var config in _regimeConfigurations.Values)
            {
                sum += config.IndicatorAlignment;
            }
            return sum / _regimeConfigurations.Count;
        }
    }

    /// <summary>
    /// Result of adaptive threshold calculation
    /// </summary>
    public class AdaptiveThresholdResult
    {
        /// <summary>
        /// Final adjusted configuration
        /// </summary>
        public RegimeConfiguration Configuration { get; set; } = new();

        /// <summary>
        /// Original base configuration before adjustments
        /// </summary>
        public RegimeConfiguration BaseConfiguration { get; set; } = new();

        /// <summary>
        /// Market regime analysis that drove the adjustments
        /// </summary>
        public MarketRegimeAnalysis RegimeAnalysis { get; set; } = new();

        /// <summary>
        /// Reason for the adjustments made
        /// </summary>
        public string AdjustmentReason { get; set; } = string.Empty;

        /// <summary>
        /// When this threshold calculation was performed
        /// </summary>
        public DateTime Timestamp { get; set; }
    }

    /// <summary>
    /// Performance statistics for adaptive threshold management
    /// </summary>
    public class AdaptiveThresholdStatistics
    {
        /// <summary>
        /// Total number of threshold adjustments made
        /// </summary>
        public long TotalAdjustments { get; set; }

        /// <summary>
        /// When the last adjustment was made
        /// </summary>
        public DateTime LastAdjustment { get; set; }

        /// <summary>
        /// Number of configured regimes
        /// </summary>
        public int ConfiguredRegimes { get; set; }

        /// <summary>
        /// Average confidence threshold across all regimes
        /// </summary>
        public double AverageConfidenceThreshold { get; set; }

        /// <summary>
        /// Average indicator alignment requirement across all regimes
        /// </summary>
        public double AverageIndicatorAlignment { get; set; }
    }
}
